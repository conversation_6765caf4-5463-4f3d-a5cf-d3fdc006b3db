using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Entities;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.DynamicForm.Application.Contract.Interfaces;

/// <summary>
/// FormInstanceData 服务接口
/// </summary>
public interface IFormInstanceDataService :
    IAppService<long, FormInstanceDataDto, FormInstanceDataQueryCriteria>,
    IEditableAppService<long, FormInstanceDataOperationDto>
{
    Task<List<FormInstanceDataDto>> GetByFormInstanceAsync(FormInstanceDto formInstanceDto);

    Task<bool> SetFormDataAsync(FormInstanceDto formInstanceDto, Dictionary<string, string?> formData);

    Task<bool> AddFormDataAsync(FormInstanceDto formInstanceDto, Dictionary<string, string?> formData);
    Task<List<FormInstanceDataDto>> GetListByFormInstanceAsync(IEnumerable<FormInstanceDto> instances);
    Expression<Func<FormInstanceDataDto, bool>> GetDtoEqualToFormInstanceExpr(FormInstanceDto formInstanceDto);
    Task<object> QueryFormDataAsync(string queryCode, Dictionary<string, string> queryParameters);
    Task<bool> ExistFormDataValueAsync(string formCode, string formDataCode, string formDataValue);
    Task<bool> ExistFormDataValueAsync(string formCode, string businessId, string formDataCode, string formDataValue);
}
