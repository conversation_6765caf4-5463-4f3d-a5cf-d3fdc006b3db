using System.Collections.Specialized;
using System.Text;
using System.Web;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Files.ApiClient;

public class DynamicFormApiClient : BaseApiClient
{
    private readonly string _baseUrl;
    private readonly DynamicFormApiClientHelper _helper;

    public DynamicFormApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption,
        ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        DynamicFormApiClientHelper helper)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext)
    {
        _helper = helper;
        _baseUrl = endpointOption.Value["DynamicForm"]!.Url.TrimEnd('/');
    }

    public async Task<FormDefinitionDto> GetDefinitionAsync(string formCode)
    {
        var url = $"{_baseUrl}/Form/definition/{formCode}";

        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }

    public async Task<string> CreateNewestFormInstanceAsync(string formCode, string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{formCode}/{businessId}/newest";

        var version = await InternalPostAsync<string>(url, new object());
        return version;
    }

    public async Task<string> CreateFormInstanceAsync(string formCode, string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{formCode}/{businessId}";

        var version = await InternalPostAsync<string>(url, new object());
        return version;
    }

    public async Task<NewFormInstanceDto> SaveWithoutInstanceAsync(string formCode,
        FormDefinitionDto formDefinitionDto)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{formCode}";

        return await InternalPostAsync<NewFormInstanceDto>(url, formDefinitionDto);
    }

    public async Task<string> SaveInstanceAsync(string businessId,
        FormDefinitionDto formDefinitionDto)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{businessId}";

        return await InternalPutAsync<string>(url, formDefinitionDto);
    }

    public async Task<bool> SaveAnnotationAsync(string businessId,
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        var url = $"{_baseUrl}/FormInstance/instance/annotations/{businessId}";

        return await InternalPostAsync<bool>(url, annotationValues);
    }


    public async Task<Dictionary<string, string?>> GetFormDataAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/formData/{businessId}";

        return await InternalGetAsync<Dictionary<string, string?>>(url);
    }

    public async Task<bool> SetFormDataAsync(string businessId, Dictionary<string, string?> formData)
    {
        var url = $"{_baseUrl}/FormInstance/instance/formData/{businessId}";

        return await InternalPostAsync<bool>(url, formData);
    }

    public async Task<FormInstanceDto> GetFormInstanceDtoAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/entity/{businessId}";

        var formInstance = await InternalGetAsync<FormInstanceDto>(url);
        return formInstance;
    }

    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId, string version)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{businessId}/{version}/newest";

        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }


    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{businessId}/newest";

        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }

    public async Task<FormDefinitionDto> GetDifferenceFormInstanceAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/diff/{businessId}";

        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }

    public async Task<FormDefinitionDto> GetNamedVersionFormInstanceAsync(string businessId, string version)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{businessId}/{version}";
        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }

    public async Task<bool> SubmitInstanceAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/submit/{businessId}";
        var result = await InternalPostAsync<bool>(url, new object());
        return result;
    }

    public async Task<List<string>?> CheckFormInstanceAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/validate/{businessId}";
        var result = await InternalGetAsync<List<string>?>(url);
        return result;
    }

    public async Task<int> GetCountAsync(string formCode, List<FormInstanceStatus>? status,
        List<FormDataDynamicQuery> query)
    {
        var url =
            $"{_baseUrl}/FormInstance/count/{formCode}";
        return await InternalPostAsync<int>(url, new GetFormInstanceCountDto()
        {
            Status = status,
            FormDataDynamicQueries = query,
            DynamicQueries = new Dictionary<string, string>()
        });
    }

    public async Task<int> GetNewestCountAsync(string formCode, List<FormInstanceStatus>? status,
        List<FormDataDynamicQuery> query)
    {
        var url =
            $"{_baseUrl}/FormInstance/newest-count/{formCode}";
        return await InternalPostAsync<int>(url, new GetFormInstanceCountDto()
        {
            Status = status,
            FormDataDynamicQueries = query,
            DynamicQueries = new Dictionary<string, string>()
        });
    }


    public async Task<bool> ExistFormDataValueAsync(string formCode, string formDataCode, string fromDataValue)
    {
        var url = $"{_baseUrl}/FormInstance/formData/exist/{formCode}/{formDataCode}/{fromDataValue}";

        return await InternalGetAsync<bool>(url);
    }

    public async Task<bool> ExistFormDataValueAsync(string formCode, string businessId, string formDataCode,
        string fromDataValue)
    {
        var url =
            $"{_baseUrl}/FormInstance/formData/exist/{formCode}/{businessId}/{formDataCode}/{HttpUtility.UrlEncode(fromDataValue)}";

        return await InternalGetAsync<bool>(url);
    }


    public async Task<FormInstancePageDto> GetPageAsync(PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        var url = $"{_baseUrl}/FormInstance/page";
        var nvc = _helper.BuildCriteriaNameValueCollection(criteria);
        url += "?" + nvc.ToUrlParameters(encodeUrl: false, encoding: Encoding.UTF8);
        return await InternalGetAsync<FormInstancePageDto>(url);
    }

    public async Task<FormInstancePageDto> GetNewestPageAsync(PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        var url = $"{_baseUrl}/FormInstance/newest-page";
        var nvc = _helper.BuildCriteriaNameValueCollection(criteria);
        url += "?" + nvc.ToUrlParameters(encodeUrl: false, encoding: Encoding.UTF8);
        return await InternalGetAsync<FormInstancePageDto>(url);
    }
}
