using Microsoft.AspNetCore.Mvc;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.Interfaces;

public interface IProjectService
{
    Task<int> GetCountAsync(ProjectStatisticsEnum category);
    Task<FormInstancePageDto> GetPageAsync(ProjectStatisticsEnum category, PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria);

    Task<bool> SaveToResultDataAsync(string formCode, string businessId, string version, string language,
        Dictionary<string, MultiTypeValue?>? dataValue);

    Task<FormDefinitionDto> GetFormDefinitionAsync();
    Task<string> SaveAsync(string businessId, FormDefinitionDto formDefinitionDto);
    Task<FormDefinitionDto> GetAsync(string businessId);
    Task<FormDefinitionDto> GetWithPreviousCompareAsync(string businessId);
    Task<bool> SubmitAsync(string businessId);
    Task<string> CreateAsync(string taskId);
    Task<string> CreateAsync(FormDefinitionDto formDefinitionDto);
    Task<bool> JudgeAsync(string businessId, JudgeProjectOperationDto input);
    Task<string> TranslateAsync(string businessId);
    Task<bool> AssignAsync(string businessId, AssignProjectOperationDto input);
    Task<bool> AssignReviewAsync(string businessId, AssignReviewProjectOperationDto input);

    Task<bool> ReturnLevel2Async(string businessId, AssignProjectReturnOperationDto input);
    Task<bool> RejectLevel4Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> ApprovalLevel4Async(string businessId);
    Task<bool> ApprovalLevel3Async(string businessId);
    Task<bool> RejectLevel3Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> RejectLevel2Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> ApprovalLevel2Async(string businessId);
    Task<bool> RejectLevel1Async(string businessId, SendNumberDto input);
    Task<bool> ApprovalLevel1Async(string businessId, SendNumberDto input);
    Task<bool> RejectLevel1EditAsync(string businessId);
    Task<bool> ApprovalLevel1EditAsync(string businessId);

    Task<bool> InsertProjectAndHistoryAsync(string formCode, string businessId, string version);

    Task<ProjectPageDto> GetProjectPageAsync([FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria);

    Task<Dictionary<string, List<OptionDto>?>?> GetSelectOptionAsync();
    Task<ProjectsDto?> GetProjectInfoAsync(long key);

    Task<bool> EditApplyAsync(string businessId, EditApplyProjectOperationDto input);
    Task<bool> EditConfirmedAsync(string businessId);
    Task<bool> EditRejectedAsync(string businessId);

    Task<bool> RecallAsync(string businessId);
    Task<List<DashboardStatisticsDto>> GetDashboardAsync();
}
