using XJ.Framework.DynamicForm.Domain.Shared.Enums;

namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

public class GetFormInstanceCountDto
{
    public List<FormInstanceStatus>? Status { get; set; }

    public List<FormDataDynamicQuery> FormDataDynamicQueries { get; set; } = new List<FormDataDynamicQuery>();

    public Dictionary<string, string> DynamicQueries { get; set; } = new Dictionary<string, string>();
}
