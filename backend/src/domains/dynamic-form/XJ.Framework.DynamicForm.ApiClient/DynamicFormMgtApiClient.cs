using System.Text;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;

namespace XJ.Framework.Files.ApiClient;

public class DynamicFormMgtApiClient : BaseApiClient
{
    private readonly string _baseUrl;
    private readonly DynamicFormApiClientHelper _helper;

    public DynamicFormMgtApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption,
        ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        DynamicFormApiClientHelper helper)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext)
    {
        _helper = helper;
        _baseUrl = endpointOption.Value["DynamicFormMgt"]!.Url.TrimEnd('/');
    }


    public async Task<int> GetCountAsync(string formCode, List<FormInstanceStatus>? status,
        List<FormDataDynamicQuery> query)
    {
        var url = $"{_baseUrl}/FormInstance/count/{formCode}";
        return await InternalPostAsync<int>(url, new GetFormInstanceCountDto()
        {
            Status = status,
            FormDataDynamicQueries = query,
            DynamicQueries = new Dictionary<string, string>()
        });
    }

    public async Task<int> GetNewestCountAsync(string formCode, List<FormInstanceStatus>? status,
        List<FormDataDynamicQuery> query)
    {
        var url = $"{_baseUrl}/FormInstance/newest-count/{formCode}";
        return await InternalPostAsync<int>(url, new GetFormInstanceCountDto()
        {
            Status = status,
            FormDataDynamicQueries = query,
            DynamicQueries = new Dictionary<string, string>()
        });
    }

    public async Task<bool> SaveInstanceAsync(string businessId,
        FormDefinitionDto formDefinitionDto)
    {
        var url = $"{_baseUrl}/FormInstance/instance/{businessId}";

        var formDefinition = await InternalPutAsync<bool>(url, formDefinitionDto);
        return formDefinition;
    }

    public async Task<FormInstanceDto> GetFormInstanceDtoAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/entity/{businessId}";

        var formInstance = await InternalGetAsync<FormInstanceDto>(url);
        return formInstance;
    }


    public async Task<Dictionary<string, string?>> GetFormDataAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/formData/{businessId}";

        return await InternalGetAsync<Dictionary<string, string?>>(url);
    }

    public async Task<bool> SetFormDataAsync(string businessId, Dictionary<string, string?> formData)
    {
        var url = $"{_baseUrl}/FormInstance/instance/formData/{businessId}";

        return await InternalPostAsync<bool>(url, formData);
    }

    public async Task<bool> ClearFormAnnotationAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/annotation/{businessId}/clear";
        return await InternalPostAsync<bool>(url, null);
    }

    public async Task<bool> RejectInstanceAsync(string businessId,
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        var url = $"{_baseUrl}/FormInstance/instance/reject/{businessId}";

        return await InternalPostAsync<bool>(url, annotationValues);
    }

    public async Task<bool> ConfirmInstanceAsync(string businessId)
    {
        var url = $"{_baseUrl}/FormInstance/instance/confirm/{businessId}";

        return await InternalPostAsync<bool>(url, null);
    }

    public async Task<bool> ExistFormDataValueAsync(string formCode, string formDataCode, string fromDataValue)
    {
        var url = $"{_baseUrl}/FormInstance/formData/exist/{formCode}/{formDataCode}/{fromDataValue}";

        return await InternalGetAsync<bool>(url);
    }

    public async Task<bool> ExistFormDataValueAsync(string formCode, string businessId, string formDataCode,
        string fromDataValue)
    {
        var url = $"{_baseUrl}/FormInstance/formData/exist/{formCode}/{businessId}/{formDataCode}/{fromDataValue}";

        return await InternalGetAsync<bool>(url);
    }

    public async Task<FormInstancePageDto> GetPageAsync(PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        var url = $"{_baseUrl}/FormInstance/page";
        var nvc = _helper.BuildCriteriaNameValueCollection(criteria);
        url += "?" + nvc.ToUrlParameters(encodeUrl: false, encoding: Encoding.UTF8);
        return await InternalGetAsync<FormInstancePageDto>(url);
    }

    public async Task<FormInstancePageDto> GetNewestPageAsync(PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        var url = $"{_baseUrl}/FormInstance/newest-page";
        var nvc = _helper.BuildCriteriaNameValueCollection(criteria);
        url += "?" + nvc.ToUrlParameters(encodeUrl: false, encoding: Encoding.UTF8);
        return await InternalGetAsync<FormInstancePageDto>(url);
    }
}
