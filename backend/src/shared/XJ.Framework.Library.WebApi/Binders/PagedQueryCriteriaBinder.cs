using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Converters;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.WebApi.Binders
{
    /// <summary>
    /// 用于绑定PagedQueryCriteria的绑定器
    /// </summary>
    public class PagedQueryCriteriaBinder : IModelBinder
    {
        private static readonly Dictionary<string, Action<IValueProvider, IPageRequestParams>> ReservedPageParams =
            new(StringComparer.OrdinalIgnoreCase)
            {
                {
                    "$pageIndex",
                    (valueProvider, pageParams) =>
                        SetPageValue<int>(valueProvider, "$pageIndex", value => pageParams.PageIndex = value)
                },
                {
                    "$pageSize",
                    (valueProvider, pageParams) =>
                        SetPageValue<int>(valueProvider, "$pageSize", value => pageParams.PageSize = value)
                }
            };

        private static readonly Dictionary<string, FieldSortDirection> StringToSortDirection =
            new(StringComparer.OrdinalIgnoreCase)
            {
                { "asc", FieldSortDirection.Ascending },
                { "desc", FieldSortDirection.Descending },
            };

        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            bindingContext.NullCheck(nameof(bindingContext));

            var model = TypeCreator.CreateInstance(bindingContext.ModelType);

            if (model is IPagedQueryCriteria pagedQueryCriteria)
            {
                BindPageParams(bindingContext.ValueProvider, pagedQueryCriteria.PageParams);
                BindOrderByParams(bindingContext.ValueProvider, pagedQueryCriteria);

                var condition = pagedQueryCriteria.InitCondition();

                FillModelProperties(bindingContext.ValueProvider, condition, bindingContext.ActionContext.HttpContext);
            }

            bindingContext.Result = ModelBindingResult.Success(model);

            return Task.CompletedTask;
        }

        private static void BindPageParams(IValueProvider valueProvider, IPageRequestParams pageParams)
        {
            foreach (KeyValuePair<string, Action<IValueProvider, IPageRequestParams>> kv in ReservedPageParams)
            {
                var valueResult = valueProvider.GetValue(kv.Key);

                if (valueResult != ValueProviderResult.None)
                    kv.Value(valueProvider, pageParams);
            }
        }

        private static void BindOrderByParams(IValueProvider valueProvider, IPagedQueryCriteria criteria)
        {
            var sortByResult = valueProvider.GetValue("$sortBy");

            if (sortByResult != ValueProviderResult.None)
            {
                var orderbyList = new List<OrderByRequestItem>();
                sortByResult.ForEach((index, result) =>
                {
                    var sortBy = result;

                    if (sortBy.IsNotEmpty())
                    {
                        orderbyList.Add(new OrderByRequestItem()
                            { DataField = sortBy, SortDirection = GetOrderBy(valueProvider, index) });
                    }
                });


                criteria.OrderBy = orderbyList.ToArray();
            }
        }

        private static FieldSortDirection GetOrderBy(IValueProvider valueProvider, int index)
        {
            var orderByResult = valueProvider.GetValue("$orderBy");

            var orderByString = "asc";

            if (orderByResult != ValueProviderResult.None)
                orderByString = orderByResult.Values.ElementAt(index)!;

            if (StringToSortDirection.TryGetValue(orderByString, out var orderBy) == false)
                orderBy = FieldSortDirection.Ascending;

            return orderBy;
        }

        private static void SetPageValue<T>(IValueProvider valueProvider, string valueName, Action<T> setter)
        {
            var valueResult = valueProvider.GetValue(valueName);

            if (valueResult != ValueProviderResult.None)
            {
                var value = DataConverter.ChangeType<string, T>(valueResult.FirstValue)!;

                setter(value);
            }
        }

        private static void FillModelProperties(IValueProvider valueProvider, object condition,
            Microsoft.AspNetCore.Http.HttpContext httpContext)
        {
            IEnumerable<PropertyInfo> properties = condition.GetType().ToPropertyDictionary().Values;

            if (!properties.Any())
            {
                return;
            }

            IDictionary<string, object?> propDict =
                BuildValueDictionary(valueProvider, condition.GetType(), httpContext);

            propDict.FillProperties(properties, condition);
        }

        /// <summary>
        /// 根据model type的属性，与valueProvider合并为一个大小写无关的字典
        /// </summary>
        /// <param name="valueProvider"></param>
        /// <param name="conditionType"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        private static IDictionary<string, object?> BuildValueDictionary(IValueProvider valueProvider,
            System.Type conditionType, Microsoft.AspNetCore.Http.HttpContext httpContext)
        {
            Dictionary<string, object?> dictionary = new(StringComparer.OrdinalIgnoreCase);

            var propertyDict = conditionType.ToPropertyDictionary();

            foreach (var kv in propertyDict)
            {
                if (dictionary.ContainsKey(kv.Key))
                    continue;

                var propType = kv.Value.PropertyType;

                // 1. 处理 Dictionary 类型
                if (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(Dictionary<,>))
                {
                    // 约定：请求参数为 propName[key]=value 形式
                    var dict = (System.Collections.IDictionary)Activator.CreateInstance(propType)!;
                    var keyType = propType.GetGenericArguments()[0];
                    var valueType = propType.GetGenericArguments()[1];

                    // 获取所有参数名
                    var allKeys = new List<string>();
                    allKeys.AddRange(httpContext.Request.Query.Keys);
                    if (httpContext.Request.HasFormContentType)
                        allKeys.AddRange(httpContext.Request.Form.Keys);

                    foreach (var paramName in allKeys.Distinct())
                    {
                        if (paramName.StartsWith(kv.Key + "[", StringComparison.CurrentCultureIgnoreCase) &&
                            paramName.EndsWith("]"))
                        {
                            var dictKeyStr =
                                paramName.Substring(kv.Key.Length + 1, paramName.Length - kv.Key.Length - 2);
                            object? dictKey = dictKeyStr;
                            var dictValueResult = valueProvider.GetValue(paramName);
                            object? dictValue = dictValueResult.FirstValue;

                            // 类型转换
                            if (keyType != typeof(string))
                                dictKey = DataConverter.ChangeType<string>(dictKeyStr, keyType);
                            if (valueType != typeof(string) && dictValue != null)
                                dictValue = DataConverter.ChangeType<string>(dictValue.ToString(), valueType);

                            dict.Add(dictKey!, dictValue);
                        }
                    }

                    dictionary.Add(kv.Key, dict);
                    continue;
                }

                // 2. 处理数组、List、普通类型（原有逻辑）
                ValueProviderResult valueResult;
                if (propType.IsArray ||
                    (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(List<>)))
                {
                    var elementType = propType.IsArray ? propType.GetElementType() : propType.GetGenericArguments()[0];
                    // 对象数组处理
                    if (elementType.IsClass && elementType != typeof(string))
                    {
                        var items = new List<object>();
                        int index = 0;
                        while (true)
                        {
                            var item = Activator.CreateInstance(elementType)!;
                            bool hasAny = false;
                            foreach (var prop in elementType.GetProperties())
                            {
                                var paramName = $"{kv.Key}[{index}][{prop.Name}]";
                                valueResult = valueProvider.GetValue(paramName);
                                if (valueResult != ValueProviderResult.None)
                                {
                                    hasAny = true;
                                    var value = DataConverter.ChangeType<string>(valueResult.FirstValue,
                                        prop.PropertyType);
                                    prop.SetValue(item, value);
                                }
                            }

                            if (!hasAny) break;
                            items.Add(item);
                            index++;
                        }

                        if (items.Count > 0)
                        {
                            if (propType.IsArray)
                                dictionary.Add(kv.Key, items.ToArray());
                            else
                                dictionary.Add(kv.Key, items);
                            continue;
                        }
                    }

                    // 原有简单数组处理（如 status[]）
                    valueResult = valueProvider.GetValue(kv.Key + "[]");
                    if (valueResult == ValueProviderResult.None)
                    {
                        // 尝试 status=1,2,3 的形式
                        valueResult = valueProvider.GetValue(kv.Key);
                        if (valueResult != ValueProviderResult.None && valueResult.FirstValue != null &&
                            valueResult.FirstValue.Contains(','))
                        {
                            var values = valueResult.FirstValue.Split(',')
                                .Select(v => DataConverter.ChangeType<string>(v.Trim(), elementType))
                                .ToArray();

                            if (propType.IsArray)
                                dictionary.Add(kv.Key, values);
                            else
                                dictionary.Add(kv.Key, values.ToList());
                            continue;
                        }
                    }

                    if (valueResult != ValueProviderResult.None)
                    {
                        var values = valueResult.Values
                            .SelectMany(v => v?.Split(',') ?? Enumerable.Empty<string>())
                            .Select(v => DataConverter.ChangeType<string>(v.Trim(), elementType))
                            .ToArray();
                        if (propType.IsArray)
                            dictionary.Add(kv.Key, values);
                        else
                            dictionary.Add(kv.Key, values.ToList());
                        continue;
                    }
                }
                else
                {
                    // 普通类型只用原始 key
                    valueResult = valueProvider.GetValue(kv.Key);
                }

                if (valueResult != ValueProviderResult.None)
                {
                    if (valueResult.Values.Count > 1)
                    {
                        if (propType.IsArray)
                        {
                            dictionary.Add(kv.Key, valueResult.Values.Select(q => q?.ToString()).ToArray());
                        }
                        else if (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(List<>))
                        {
                            dictionary.Add(kv.Key, valueResult.Values.Select(q => q?.ToString()).ToList());
                        }
                        else
                        {
                            dictionary.Add(kv.Key, valueResult.Values.Select(q => q?.ToString()).ToList());
                        }
                    }
                    else
                    {
                        dictionary.Add(kv.Key, valueResult.FirstValue);
                    }
                }
            }

            return dictionary;
        }
    }
}