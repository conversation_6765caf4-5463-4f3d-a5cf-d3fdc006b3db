using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.Library.Application.Contract.Extensions;

/// <summary>
/// 查询条件转换为WhereItem的扩展方法
/// </summary>
public static class QueryCriteriaToWhereItemExtensions
{
    /// <summary>
    /// 将PagedQueryCriteria转换为WhereItem列表
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <returns>WhereItem列表</returns>
    public static List<WhereItem> ToWhereItems<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        if (criteria?.Condition == null)
            return new List<WhereItem>();

        var whereItems = new List<WhereItem>();
        var properties = typeof(TQueryCriteria).GetProperties();

        foreach (var property in properties)
        {
            var queryOperator = property.GetCustomAttributes(typeof(QueryOperatorAttribute), true)
                .FirstOrDefault() as QueryOperatorAttribute;

            if (queryOperator == null) continue;

            var propertyValue = property.GetValue(criteria.Condition);
            if (propertyValue == null) continue;

            var propertyName = queryOperator.PropertyName ?? property.Name;
            var entityProperty = typeof(TEntity).GetProperty(propertyName);
            if (entityProperty == null)
                throw new InvalidOperationException(
                    $"Property '{propertyName}' not found on type '{typeof(TEntity).Name}'.");

            var dataField = GetDatabaseColumnName(entityProperty);
            var fieldType = entityProperty.PropertyType;

            var whereItem = new WhereItem
            {
                DataField = dataField,
                Operator = ConvertToWhereOperator(queryOperator.Operator),
                Value = propertyValue,
                FieldType = fieldType
            };

            whereItems.Add(whereItem);
        }

        return whereItems;
    }


    /// <summary>
    /// 将PagedQueryCriteria的OrderBy转换为OrderByItem列表
    /// </summary>
    /// <typeparam name="TKey">实体主键类型</typeparam>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <typeparam name="TQueryCriteria">查询条件类型</typeparam>
    /// <param name="criteria">分页查询条件</param>
    /// <returns>OrderByItem列表</returns>
    public static List<OrderByItem> ToOrderByItems<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        return criteria.OrderBy.ToOrderByItems<TKey, TEntity>();
    }

    public static List<OrderByItem> ToOrderByItems<TKey, TEntity>(
        this IEnumerable<IOrderByRequestItem> orderBy)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
    {
        return ToOrderByItems<TKey, TEntity>(orderBy.ToArray());
    }

    public static List<OrderByItem> ToOrderByItems<TKey, TEntity>(
        this List<IOrderByRequestItem> orderBy)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
    {
        return ToOrderByItems<TKey, TEntity>(orderBy.ToArray());
    }

    public static List<OrderByItem> ToOrderByItems<TKey, TEntity>(
        this IOrderByRequestItem[] orderBy)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
    {
        if (orderBy == null || !orderBy.Any())
            return new List<OrderByItem>();

        var orderByItems = new List<OrderByItem>();

        foreach (var orderByRequestItem in orderBy)
        {
            var propertyName = QueryCriteriaExtensions.CapitalizeFirstLetter(orderByRequestItem.DataField);
            var entityProperty = typeof(TEntity).GetProperty(propertyName);
            if (entityProperty == null)
                throw new InvalidOperationException(
                    $"Property '{propertyName}' not found on type '{typeof(TEntity).Name}'.");

            var dataField = GetDatabaseColumnName(entityProperty);

            var orderByItem = new OrderByItem
            {
                DataField = dataField,
                SortDirection = ConvertToOrderByDirection(orderByRequestItem.SortDirection)
            };

            orderByItems.Add(orderByItem);
        }

        return orderByItems;
    }

    /// <summary>
    /// 获取数据库列名
    /// </summary>
    /// <param name="propertyInfo">属性信息</param>
    /// <returns>数据库列名</returns>
    private static string GetDatabaseColumnName(PropertyInfo propertyInfo)
    {
        var columnAttribute = propertyInfo.GetCustomAttribute<ColumnAttribute>();
        return columnAttribute?.Name ?? propertyInfo.Name.ToLower();
    }

    /// <summary>
    /// 转换查询操作符
    /// </summary>
    /// <param name="queryOperator">查询操作符</param>
    /// <returns>WhereOperator</returns>
    private static WhereOperator ConvertToWhereOperator(QueryOperator queryOperator)
    {
        return queryOperator switch
        {
            QueryOperator.Equal => WhereOperator.Equal,
            QueryOperator.NotEqual => WhereOperator.NotEqual,
            QueryOperator.Contains => WhereOperator.Contains,
            QueryOperator.GreaterThan => WhereOperator.GreaterThan,
            QueryOperator.GreaterThanOrEqual => WhereOperator.GreaterThanOrEqual,
            QueryOperator.LessThan => WhereOperator.LessThan,
            QueryOperator.LessThanOrEqual => WhereOperator.LessThanOrEqual,
            QueryOperator.StartsWith => WhereOperator.StartsWith,
            QueryOperator.EndsWith => WhereOperator.EndsWith,
            QueryOperator.Between => WhereOperator.Between,
            QueryOperator.In => WhereOperator.In,
            _ => throw new NotSupportedException($"Operator {queryOperator} is not supported")
        };
    }

    /// <summary>
    /// 转换排序方向
    /// </summary>
    /// <param name="sortDirection">排序方向</param>
    /// <returns>OrderByDirection</returns>
    private static OrderByDirection ConvertToOrderByDirection(FieldSortDirection sortDirection)
    {
        return sortDirection switch
        {
            FieldSortDirection.Ascending => OrderByDirection.Ascending,
            FieldSortDirection.Descending => OrderByDirection.Descending,
            _ => throw new NotSupportedException($"Sort direction {sortDirection} is not supported")
        };
    }
}
