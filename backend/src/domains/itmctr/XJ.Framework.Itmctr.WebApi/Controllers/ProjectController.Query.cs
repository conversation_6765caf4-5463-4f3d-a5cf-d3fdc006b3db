using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Itmctr.WebApi.Controllers;

public partial class ProjectController
{
    [HttpGet("projectUserAllList")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> ProjectUserAllListAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.UserAll, criteria);
    }
    [HttpGet("projectUserPendingSubmit")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> ProjectUserPendingSubmitAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.UserPendingSubmit, criteria);
    }
    [HttpGet("projectUserPendingApproval")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> ProjectUserPendingApprovalAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.UserPendingApproval, criteria);
    }
    [HttpGet("projectUserApprovedList")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> ProjectUserApprovedAsync(
        [FromQuery] PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(ProjectStatisticsEnum.UserApproved, criteria);
    }


    private async Task<FormInstancePageDto> GetPageAsync(
        ProjectStatisticsEnum category,
        PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await _projectService.GetPageAsync(category, criteria);
    }
}
