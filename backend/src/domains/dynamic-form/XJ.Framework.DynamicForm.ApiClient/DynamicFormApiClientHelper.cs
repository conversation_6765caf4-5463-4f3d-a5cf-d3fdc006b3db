using System.Collections.Specialized;
using System.Text;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Application.Contract.QueryCriteria;

namespace XJ.Framework.Files.ApiClient;

public class DynamicFormApiClientHelper
{
    public NameValueCollection BuildCriteriaNameValueCollection(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        var query = new NameValueCollection
        {
            { "$pageIndex", criteria.PageParams.PageIndex.ToString() },
            { "$pageSize", criteria.PageParams.PageSize.ToString() },
            { "formCode", criteria.Condition.FormCode },
            { "isObsoleted", criteria.Condition.IsObsoleted?.ToString() ?? "false" }
        };
        if (criteria.Condition.Status is { Count: > 0 })
        {
            foreach (var status in criteria.Condition.Status)
            {
                query.Add($"status[]", ((int)status).ToString());
            }
        }

        if (criteria.Condition.FormDataDynamicQueries.Any())
        {
            var index = 0;
            foreach (var formDataDynamicQuery in criteria.Condition.FormDataDynamicQueries)
            {
                query.Add($"formDataDynamicQueries[{index}][key]", formDataDynamicQuery.Key);
                query.Add($"formDataDynamicQueries[{index}][operator]",
                    ((int)formDataDynamicQuery.Operator).ToString());
                query.Add($"formDataDynamicQueries[{index}][value]", formDataDynamicQuery.Value ?? string.Empty);
                index++;
            }
        }

        if (criteria.Condition.DynamicQueries.Any())
        {
            foreach (var dynamicQuery in criteria.Condition.DynamicQueries)
            {
                query.Add($"dynamicQueries[{dynamicQuery.Key}]", dynamicQuery.Value);
            }
        }

        if (criteria.OrderBy.Any())
        {
            foreach (var orderBy in criteria.OrderBy)
            {
                query.Add($"$sortBy", orderBy.DataField);
                query.Add($"$orderBy", orderBy.SortDirection == FieldSortDirection.Ascending ? "asc" : "desc");
            }
        }

        return query;
    }
}
