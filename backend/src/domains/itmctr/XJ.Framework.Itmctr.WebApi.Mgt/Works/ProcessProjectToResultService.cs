using AutoMapper;
using System.Text.Json;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Itmctr.WebApi.Mgt.Works;

public class ProcessProjectToResultService : BackgroundService
{
    private readonly ILogger<ProcessProjectToResultService> _logger;
    private readonly IProjectService _projectService;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IMapper _mapper;


    public ProcessProjectToResultService(ILogger<ProcessProjectToResultService> logger,
        IServiceScopeFactory serviceScopeFactory, IMapper mapper)
    {
        _logger = logger;
        var scope = serviceScopeFactory.CreateScope();
        _projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
        _asyncTaskService = scope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
        _mapper = mapper;
    }

    protected async override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var pending = await _asyncTaskService.GetPageAsync(new PagedQueryCriteria<AsyncTaskQueryCriteria>()
            {
                Condition = new AsyncTaskQueryCriteria
                {
                    TaskStatus = AsyncTaskStatus.Pending,
                    TaskCode = "ProcessProjectToResult"
                },
                PageParams = new PageRequestParams(1, 10)
            });

            await pending.Rows.ForEachAsync(async asyncTask =>
            {
                var dict = JsonSerializer.Deserialize<Dictionary<string, object?>>(asyncTask.TaskData)!;

                var businessId = dict["BusinessId"]!.ToString()!;
                var version = dict["Version"]!.ToString()!;
                var formCode = dict["FormCode"]!.ToString()!;
                var language = dict["Language"]!.ToString()!;
                var dataValue =
                    JsonSerializer.Deserialize<Dictionary<string, MultiTypeValue?>>(dict["DataValue"]!.ToString()!);

                var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);
                try
                {
                    _logger.LoggingInformation("project-to-result",
                        "开始处理项目到结果表,BusinessId:{BusinessId},Version:{Version}", businessId, version);


                    // asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Processing;
                    // await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);


                    _logger.LoggingInformation("project-to-result",
                        "开始处理项目到结果表,BusinessId:{BusinessId},Version:{Version}", businessId, version);

                    await _projectService.SaveToResultDataAsync(formCode, businessId, version, language, dataValue);

                    asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Completed;
                    await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);

                    _logger.LoggingInformation("project-to-result",
                        "处理项目到结果表完成,BusinessId:{BusinessId},Version:{Version}", businessId, version);
                }
                catch (Exception ex)
                {
                    _logger.LoggingException("project-to-result", ex,
                        "处理项目到结果表失败,BusinessId:{BusinessId},Version:{Version}", businessId, version);

                    asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Failed;
                    asyncTaskOperationDto.TaskResult = ex.Message;
                    await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
                }
            });

            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }
}
