using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.SqlServer.Server;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Application.Options;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Repositories.Interfaces;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Rbac.ApiClient;
using XJ.Framework.Rbac.EntityFrameworkCore.Repositories;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ProjectService : IProjectService
{
    private readonly DynamicFormApiClient _dynamicFormApiClient;
    private readonly DynamicFormMgtApiClient _dynamicFormMgtApiClient;
    private const string FormCode = "PROJECT";
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IFormRecognitionService _formRecognitionService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly UserMgtApiClient _userMgtApiClient;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    private readonly IOptions<UnicomOption> _unicomOption;
    private readonly UserApiClient _userApiClient;
    private readonly IProjectRepository _projectRepository;
    private readonly IProjectAttachRepository _projectAttachRepository;
    private readonly IProjectAttachHistoryRepository _projectAttachHistoryRepository;
    private readonly IProjectSponsorRepository _projectSponsorRepository;
    private readonly IProjectSponsorHistoryRepository _projectSponsorHistoryRepository;
    private readonly IProjectResearchSiteRepository _projectResearchSiteRepository;
    private readonly IProjectResearchSiteHistoryRepository _projectResearchSiteHistoryRepository;
    private readonly IProjectInterventionRepository _projectInterventionRepository;
    private readonly IProjectInterventionHistoryRepository _projectInterventionHistoryRepository;
    private readonly IProjectMeasurementRepository _projectMeasurementRepository;
    private readonly IProjectMeasurementHistoryRepository _projectMeasurementHistoryRepository;
    private readonly IProjectHumanSampleRepository _projectHumanSampleRepository;
    private readonly IProjectHumanSampleHistoryRepository _projectHumanSampleHistoryRepository;
    private readonly IProjectHistoryRepository _projectHistoryRepository;

    private readonly IUnitOfWork _unitOfWork;

    public ProjectService(DynamicFormApiClient dynamicFormApiClient, DynamicFormMgtApiClient dynamicFormMgtApiClient,
        IAsyncTaskService asyncTaskService, IFormRecognitionService formRecognitionService, IMapper mapper,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext, UserMgtApiClient userMgtApiClient, IOptions<JsonOptions> jsonOptions,
        IOptions<UnicomOption> unicomOption, UserApiClient userApiClient, IProjectRepository projectRepository,
        IProjectAttachRepository projectAttachRepository, IProjectSponsorRepository projectSponsorRepository,
        IProjectResearchSiteRepository projectResearchSiteRepository,
        IProjectInterventionRepository projectInterventionRepository,
        IProjectMeasurementRepository projectMeasurementRepository,
        IProjectHumanSampleRepository projectHumanSampleRepository,
        IUnitOfWork unitOfWork, IProjectHistoryRepository projectHistoryRepository,
        IProjectAttachHistoryRepository projectAttachHistoryRepository,
        IProjectSponsorHistoryRepository projectSponsorHistoryRepository,
        IProjectResearchSiteHistoryRepository projectResearchSiteHistoryRepository,
        IProjectInterventionHistoryRepository projectInterventionHistoryRepository,
        IProjectMeasurementHistoryRepository projectMeasurementHistoryRepository,
        IProjectHumanSampleHistoryRepository projectHumanSampleHistoryRepository
    )
    {
        _dynamicFormApiClient = dynamicFormApiClient;
        _dynamicFormMgtApiClient = dynamicFormMgtApiClient;
        _asyncTaskService = asyncTaskService;
        _formRecognitionService = formRecognitionService;
        _mapper = mapper;
        _currentUserContext = currentUserContext;
        _userMgtApiClient = userMgtApiClient;
        _unicomOption = unicomOption;
        _userApiClient = userApiClient;
        this._jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
        _projectRepository = projectRepository;
        _projectAttachRepository = projectAttachRepository;
        _projectSponsorRepository = projectSponsorRepository;
        _projectResearchSiteRepository = projectResearchSiteRepository;
        _projectInterventionRepository = projectInterventionRepository;
        _projectMeasurementRepository = projectMeasurementRepository;
        _projectHumanSampleRepository = projectHumanSampleRepository;
        _projectHistoryRepository = projectHistoryRepository;
        _projectAttachHistoryRepository = projectAttachHistoryRepository;
        _projectSponsorHistoryRepository = projectSponsorHistoryRepository;
        _projectResearchSiteHistoryRepository = projectResearchSiteHistoryRepository;
        _projectInterventionHistoryRepository = projectInterventionHistoryRepository;
        _projectMeasurementHistoryRepository = projectMeasurementHistoryRepository;
        _projectHumanSampleHistoryRepository = projectHumanSampleHistoryRepository;
        _unitOfWork = unitOfWork;
    }


    private async Task<FormDefinitionDto> FilterFormDefinitionAsync(FormDefinitionDto formDefinition)
    {
        formDefinition.Groups.ForEach(group =>
        {
            //过滤掉注册号字段 如果只取不是注册号或者是注册号但值不为空的
            group.Fields = group.Fields.Where(field =>
                field.Code != "registration_number" || (field.Code == "registration_number" &&
                                                        !(field.Value?.StringValue?.IsNullOrEmpty() ?? true))).ToList();
            var registrationNumberField = group.Fields.FirstOrDefault(q => q.Code == "registration_number");
            if (registrationNumberField != null)
            {
                group.Fields.FirstOrDefault(q => q.Code == "registration_number")!.InitReadonly = true;
            }
        });
        return await Task.FromResult(formDefinition);
    }

    public async Task<FormDefinitionDto> GetFormDefinitionAsync()
    {
        var formDefinitionDto = await _dynamicFormApiClient.GetDefinitionAsync(FormCode);
        return await FilterFormDefinitionAsync(formDefinitionDto);
    }


    public async Task<string> SaveAsync(string businessId, FormDefinitionDto formDefinitionDto)
    {
        var originalFormDefinition = await GetAsync(businessId);

        //如果已经审批通过
        if (originalFormDefinition.FormData.TryGetValue("RegistrationNumber", out var registrationNumber)
            && !string.IsNullOrEmpty(registrationNumber)
           )
        {
            if (!originalFormDefinition.FormData.TryGetValue("EditProcessStatus", out var editProcessStatus) ||
                editProcessStatus != "Approved")
            {
                throw new ValidationException(
                    "您还未发起再修改申请或申请未通过/You have not initiated a modification request or the request has not been approved");
            }
        }

        formDefinitionDto.FormData = originalFormDefinition.FormData;

        await _dynamicFormApiClient.SaveInstanceAsync(businessId, formDefinitionDto);

        return businessId;
    }

    public async Task<FormDefinitionDto> GetAsync(string businessId)
    {
        var formDefinitionDto = await _dynamicFormApiClient.GetNewestFormInstanceAsync(businessId);

        return await FilterFormDefinitionAsync(formDefinitionDto);
    }

    public async Task<FormDefinitionDto> GetWithPreviousCompareAsync(string businessId)
    {
        var formDefinitionDto = await _dynamicFormApiClient.GetDifferenceFormInstanceAsync(businessId);

        return await FilterFormDefinitionAsync(formDefinitionDto);
    }


    public async Task<bool> SubmitAsync(string businessId)
    {
        var formInstanceDto = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstanceDto.Status != FormInstanceStatus.Draft ||
            formInstanceDto.ApplyUserId != _currentUserContext.GetCurrentUserId())
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }


        var result = await _dynamicFormApiClient.CheckFormInstanceAsync(businessId);
        if (result == null || result.Any())
        {
            throw new ValidationException(string.Join("\n", result!));
        }

        //调用获取表单定义
        var formDefinitionDto = await _dynamicFormApiClient.GetDifferenceFormInstanceAsync(businessId);


        //如果已经审批通过
        if (formDefinitionDto.FormData.TryGetValue("RegistrationNumber", out var registrationNumber)
            && !string.IsNullOrEmpty(registrationNumber)
           )
        {
            if (!formDefinitionDto.FormData.TryGetValue("EditProcessStatus", out var editProcessStatus) ||
                editProcessStatus != "Approved")
            {
                throw new ValidationException(
                    "您还未发起再修改申请或申请未通过/You have not initiated a modification request or the request has not been approved");
            }
        }


        var publicTitle =
            (formDefinitionDto.GetFieldValue("public_title")?.KeyValue?["zh"] ??
             formDefinitionDto.GetFieldValue("public_title")?.KeyValue?["en"])?.ToString() ?? string.Empty;

        if (publicTitle.IsNullOrEmpty())
        {
            throw new ValidationException("注册题目不能为空/Public title is required");
        }

        var existPublicTitle =
            await _dynamicFormApiClient.ExistFormDataValueAsync(FormCode, businessId, "PublicTitle", publicTitle);

        if (existPublicTitle)
        {
            throw new ValidationException("注册题目已存在，请重新输入/Public title already exists, please re-enter.");
        }

        //获取批注
        var annotationValues = await FormDefinitionHelper.GetAnnotationsAsync(formDefinitionDto);

        //获取上一版本差异值
        var previousValues = await FormDefinitionHelper.GetFieldPreviousValuesAsync(formDefinitionDto);

        //获取当前值
        var values = await FormDefinitionHelper.GetFieldValuesAsync(formDefinitionDto);

        //获取上一版本的关键文件
        var previousFormRecognitionContext = await GetCriticalFilesAsync(previousValues);

        //获取当前版本的关键文件
        var formRecognitionContext = await GetCriticalFilesAsync(values);


        //获取当前表单中保存的TaskId
        var taskId = formDefinitionDto.FormData.TryGetValue("TaskId", out var value) ? value : null;

        if (_unicomOption.Value.Enable && formRecognitionContext is
                { Ethics: not null, Consent: not null, Protocol: not null })
        {
            // 如果之前没做过预检查，或者有上一版本并且关键文件有变更，则需要重新做预检查
            if (taskId.IsNullOrEmpty() ||
                (!formDefinitionDto.PreviousVersion.IsNullOrEmpty()
                 &&
                 await CheckCriticalFilesChangeAsync(previousFormRecognitionContext, formRecognitionContext)))
            {
                //调用同步预检查
                var preCheckResult = await _formRecognitionService.PreCheckAsync(formRecognitionContext);

                taskId = preCheckResult.TaskId;

                //重新赋值TaskId
                formDefinitionDto.FormData["TaskId"] = taskId;
            }
        }

        formDefinitionDto.FormData["PublicTitle"] = publicTitle;

        //更新提交时间
        formDefinitionDto.FormData["SubmitTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        //如果没有首次提交时间 则赋值
        if (!formDefinitionDto.FormData.ContainsKey("FirstSubmitTime"))
        {
            formDefinitionDto.FormData["FirstSubmitTime"] = formDefinitionDto.FormData["SubmitTime"];
        }

        formDefinitionDto.FormData.TryGetValue("ProcessStatus", out var processStatus);
        //先重置流程状态
        formDefinitionDto.FormData["ProcessStatus"] = string.Empty;

        //如果原来有审批状态并且状态时驳回到发起人 则直接修改为等待四级审核
        if (!string.IsNullOrEmpty(processStatus))
        {
            //说明是再修改
            if (!string.IsNullOrEmpty(registrationNumber))
            {
                formDefinitionDto.FormData["ProcessStatus"] = "PendingFourthApproval";
            }
            else if (processStatus == "RejectToApply")
            {
                //说明是驳回的
                formDefinitionDto.FormData["ProcessStatus"] = "PendingFourthApproval";
            }
        }

        if (!formDefinitionDto.FormData.ContainsKey("TraditionalProject"))
        {
            formDefinitionDto.FormData["TraditionalProject"] = string.Empty;
        }

        if (!formDefinitionDto.FormData.ContainsKey("RegistrationNumber"))
        {
            formDefinitionDto.FormData["RegistrationNumber"] = string.Empty;
        }

        //移除指定的批注值（翻译和填充）
        await FormDefinitionHelper.RemoveNamedAnnotationsAsync(annotationValues, "fill", "translate");

        //调用保存批注
        await _dynamicFormApiClient.SaveAnnotationAsync(businessId, annotationValues);

        //保存表单数据
        AddApprovalHistory("User", "Submit", string.Empty, formDefinitionDto.FormData);
        await _dynamicFormApiClient.SetFormDataAsync(businessId, formDefinitionDto.FormData);

        //调用提交
        await _dynamicFormApiClient.SubmitInstanceAsync(businessId);

        if (_unicomOption.Value.Enable)
        {
            //发起内容对比服务
            await _formRecognitionService.ContentCompareAsync(taskId!, formDefinitionDto);
        }

        return true;
    }

    private async Task<bool> CheckCriticalFilesChangeAsync(FormRecognitionContext previousFormRecognitionContext,
        FormRecognitionContext formRecognitionContext)
    {
        var ethicsChanged = previousFormRecognitionContext.Ethics?.FileId != formRecognitionContext.Ethics?.FileId;
        var protocolChanged =
            previousFormRecognitionContext.Protocol?.FileId != formRecognitionContext.Protocol?.FileId;
        var consentChanged = previousFormRecognitionContext.Consent?.FileId != formRecognitionContext.Consent?.FileId;

        return await Task.FromResult(ethicsChanged || protocolChanged || consentChanged);
    }

    private async Task<FormRecognitionContext> GetCriticalFilesAsync(Dictionary<string, MultiTypeValue?>? dataValue)
    {
        var ethicsValue = dataValue?["ethic_committee_approved_file"]?.KeyValue;

        var protocolValue = dataValue?["study_protocol"]?.KeyValue;

        var consentValue = dataValue?["informed_consent_file"]?.KeyValue;

        var ethics = ethicsValue != null
            ? new FileDto()
            {
                FileId = ethicsValue!["fileId"]!.ToString()!,
                FileName = ethicsValue!["fileName"]!.ToString()!,
            }
            : null;
        var protocol = protocolValue != null
            ? new FileDto()
            {
                FileId = protocolValue!["fileId"]!.ToString()!,
                FileName = protocolValue!["fileName"]!.ToString()!,
            }
            : null;
        var consent = consentValue != null
            ? new FileDto()
            {
                FileId = consentValue["fileId"]!.ToString()!,
                FileName = consentValue["fileName"]!.ToString()!,
            }
            : null;
        return await Task.FromResult(new FormRecognitionContext()
        {
            Ethics = ethics,
            Protocol = protocol,
            Consent = consent
        });
    }

    public async Task<string> CreateAsync(FormDefinitionDto formDefinitionDto)
    {
        // 创建实例
        var result = await _dynamicFormApiClient.SaveWithoutInstanceAsync(FormCode, formDefinitionDto);
        return result.BusinessId;
    }


    public async Task<string> TranslateAsync(string businessId)
    {
        var formDefinitionDto = await GetAsync(businessId);
        var requestId = await _formRecognitionService.ContentTranslateAsync(formDefinitionDto);

        var formData = await _dynamicFormApiClient.GetFormDataAsync(businessId);
        formData["TranslateRequestId"] = requestId;
        await _dynamicFormApiClient.SetFormDataAsync(businessId, formData);

        return requestId;
    }

    public async Task<string> CreateAsync(string taskId)
    {
        //获取表单定义
        var formDefinition = await _dynamicFormApiClient.GetDefinitionAsync(FormCode);

        await _formRecognitionService.FillFormAsync(taskId, FillFormLanguage.Chinese, "Extract", "fill",
            formDefinition);

        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "Extract");

        if (asyncTask != null)
        {
            var context = JsonSerializer.Deserialize<FormRecognitionContext>(asyncTask.TaskData);

            if (context != null)
            {
                formDefinition.Groups.ForEach(group =>
                {
                    group.Fields.ForEach(field =>
                    {
                        if (field.Code == "ethic_committee_approved_file")
                        {
                            field.Value = context.Ethics != null
                                ? new MultiTypeValue()
                                {
                                    KeyValue = new Dictionary<string, object?>()
                                    {
                                        { "fileId", context.Ethics.FileId },
                                        { "fileName", context.Ethics.FileName },
                                        { "fileSize", context.Ethics.FileSize }
                                    }
                                }
                                : null;
                        }
                        else if (field.Code == "study_protocol")
                        {
                            field.Value = context.Protocol != null
                                ? new MultiTypeValue()
                                {
                                    KeyValue = new Dictionary<string, object?>()
                                    {
                                        { "fileId", context.Protocol.FileId },
                                        { "fileName", context.Protocol.FileName },
                                        { "fileSize", context.Protocol.FileSize }
                                    }
                                }
                                : null;
                        }
                        else if (field.Code == "informed_consent_file")
                        {
                            field.Value = context.Consent != null
                                ? new MultiTypeValue()
                                {
                                    KeyValue = new Dictionary<string, object?>()
                                    {
                                        { "fileId", context.Consent.FileId },
                                        { "fileName", context.Consent.FileName },
                                        { "fileSize", context.Consent.FileSize }
                                    }
                                }
                                : null;
                        }
                        else if (field.Code == "ethic_committee_approved")
                        {
                            field.Value = new MultiTypeValue()
                            {
                                StringValue = "1"
                            };
                        }
                    });
                });
            }
        }

        // 设置formData
        var formData = new Dictionary<string, string?>()
        {
            {
                "TaskId", taskId
            }
        };

        formDefinition.FormData = formData;

        return await CreateAsync(formDefinition);
    }


    public async Task<ProjectPageDto> GetProjectPageAsync(
        [FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria)
    {
        var whereExpr = criteria.Condition.BuildExpression<long, ProjectEntity, ProjectQueryCriteria>();

        var orderbyExpr = criteria.BuildOrderExpression<long, ProjectEntity, ProjectQueryCriteria>();
        var data = await _projectRepository.GetProjectPageAsync(
            whereExpr,
            criteria.Condition.DynamicQueries,
            criteria.PageParams.ToRowIndex(),
            criteria.PageParams.PageSize,
            orderbyExpr
        );
        return await ConvertProjectPageDataAsync(data);
    }

    public async Task<Dictionary<string, List<OptionDto>?>?> GetSelectOptionAsync()
    {
        var options = new Dictionary<string, List<OptionDto>?>();
        var formDefinitionDto = await _dynamicFormApiClient.GetDefinitionAsync(FormCode);
        foreach (var group in formDefinitionDto.Groups)
        {
            // 遍历Group下所有Field，筛选类型为select的
            foreach (var field in group.Fields.Where(f => f.Type == "select" || f.Type == "radio"))
            {
                // field.Options 为 List<OptionDto>?
                options[field.Code] = field?.Options;
            }
        }

        return options;
    }

    private async Task<ProjectPageDto> ConvertProjectPageDataAsync(PageData<long, ProjectEntity> data)
    {
        // 获取所有业务ID
        var businessIds = data.Rows.Select(x => x.BusinessId).Distinct().ToList();
        // 批量查询所有相关历史记录
        var historyList = await _projectHistoryRepository.GetListAsync(q => businessIds.Contains(q.BusinessId));
        // 映射并赋值 first_submit_time
        var dtoList = data.Rows.Select(project =>
        {
            var dto = _mapper.Map<ProjectsDto>(project);
            var firstHistory = historyList
                .Where(h => h.BusinessId == project.BusinessId)
                .OrderBy(h => h.CreatedTime)
                .FirstOrDefault();
            dto.first_submit_time = firstHistory?.CreatedTime ?? project.CreatedTime;
            return dto;
        }).ToList();

        // 构造 ProjectPageDto
        var result = new ProjectPageDto
        {
            Totals = data.Totals,
            Rows = dtoList
        };
        return result;
    }

    public async Task<ProjectsDto?> GetProjectInfoAsync(long key)
    {
        var project = await _projectRepository.GetAsync(key);
        if (project == null)
        {
            throw new ValidationException("记录不存在/Record does not exist");
        }

        ProjectsDto dto = new ProjectsDto() { Key = 0 };
        _mapper.Map(project, dto);
        //files
        //var files = await _projectAttachRepository.GetListAsync(q => q.ProjectId == project.Key);
        //试验主办单位
        var existSponsorList =
            await _projectSponsorRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.sponsor = _mapper.Map<List<ProjectSponsorDto>>(existSponsorList);
        // 研究实施地点
        var existSiteList =
            await _projectResearchSiteRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.researchSite = _mapper.Map<List<ProjectResearchSiteDto>>(existSiteList);
        // 干预措施
        var existInterventionList =
            await _projectInterventionRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.intervention = _mapper.Map<List<ProjectInterventionDto>>(existInterventionList);
        // 测量指标
        var existMeasurementList =
            await _projectMeasurementRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.measurement = _mapper.Map<List<ProjectMeasurementDto>>(existMeasurementList);
        // 采集人体标本
        var existHumanSampleList =
            await _projectHumanSampleRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.humanSample = _mapper.Map<List<ProjectHumanSampleDto>>(existHumanSampleList);
        return dto;
    }
}
