using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Itmctr.Domain.Shared.Enums;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class ProjectStatisticsDefine
{
    public string Title { get; set; } = null!;
    public string Route { get; set; } = null!;

    public string? CurrentUserIdFieldName { get; set; } = null!;

    public List<FormInstanceStatus>? Status { get; set; }
    public bool UsingNewest { get; set; } = true;

    public bool Mgt { get; set; } = false;
    public List<FormDataDynamicQuery> FormDataQueries { get; set; } = new();
}

public static class ProjectStatisticsDefineExtensions
{
    public static Dictionary<ProjectStatisticsEnum, ProjectStatisticsDefine> Map = new()
    {
        {
            ProjectStatisticsEnum.UserAll, new()
            {
                Title = "我的项目",
                Route = "projectUserAllList",
                Status = null,
                FormDataQueries = [],
                Mgt = false,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.UserPendingSubmit, new()
            {
                Title = "待提交项目",
                Route = "projectUserPendingSubmit",
                Status = [FormInstanceStatus.Draft, FormInstanceStatus.Rejected],
                FormDataQueries = [new(key: "EditProcessStatus", @operator: FormDataQueryOperator.Empty, value: "")],
                Mgt = false,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.UserPendingApproval, new()
            {
                Title = "待审核项目",
                Route = "projectUserPendingApproval",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries = [],
                Mgt = false,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.UserApproved, new()
            {
                Title = "已通过项目",
                Route = "projectUserApprovedList",
                Status = [FormInstanceStatus.Confirmed, FormInstanceStatus.Draft],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal, value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Approved")
                ],
                Mgt = false,
                UsingNewest = true
            }
        },

        {
            ProjectStatisticsEnum.Level1PendingJudge, new()
            {
                Title = "待判断项目",
                Route = "projectSystemPendingJudgeList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Empty, value: "")
                ],
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level1PendingSendNumber, new()
            {
                Title = "待发号项目",
                Route = "projectSystemPendingSendNumberList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingFirstApproval"),
                    new(key: "RegistrationNumber", @operator: FormDataQueryOperator.Empty,
                        value: "")
                ],
                CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level1ApplyEditPendingConfirmation, new()
            {
                Title = "再修改申请列表",
                Route = "projectSystemApplyEditList",
                Status = [FormInstanceStatus.Draft],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Approved"),
                    new(key: "EditProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingFirstConfirmation")
                ],
                CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level1ApplyEditPendingApproval, new()
            {
                Title = "再修改复核项目",
                Route = "projectSystemPendingReviewList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingFirstApproval"),
                    new(key: "RegistrationNumber", @operator: FormDataQueryOperator.NotEmpty,
                        value: "")
                ],
                CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level1ApplyEditReturned, new()
            {
                Title = "再修改退回列表",
                Route = "projectSystemReturnEditList",
                Status = [FormInstanceStatus.Draft, FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Approved"),
                    new(key: "EditProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Rejected")
                ],
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level1SentNumber, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Approved")
                ],
                CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = false
            }
        },
        {
            ProjectStatisticsEnum.Level1NonTraditional, new()
            {
                Title = "非传统医学项目",
                Route = "projectSystemNonTraditionalList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "False")
                ],
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level1AllSubmitted, new()
            {
                Title = "审核状态查询",
                Route = "projectSystemAllSubmittedList",
                Status = [FormInstanceStatus.Submitted, FormInstanceStatus.Rejected],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.NotEqual,
                        value: "False")
                ],
                Mgt = true,
                UsingNewest = true
            }
        },


        {
            ProjectStatisticsEnum.Level2PendingAssign, new()
            {
                Title = "待分配项目",
                Route = "projectSystemPendingAssignList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingSecondAssignment")
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level2PendingReview, new()
            {
                Title = "待核审项目",
                Route = "projectSystemPendingReviewList2",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingSecondApproval"),
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level2ReviewReturned, new()
            {
                Title = "已退回项目",
                Route = "projectSystemReviewReturnedList2",
                Status = [FormInstanceStatus.Draft, FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.NotEqual,
                        value: "PendingFirstApproval")
                ],
                CurrentUserIdFieldName = "LastApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level2PendingApproval, new()
            {
                Title = "已核审通过项目",
                Route = "projectSystemPendingApprovedList2",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingFirstApproval"),
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level2Approved, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList2",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Approved")
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = false
            }
        },
        {
            ProjectStatisticsEnum.Level2ReAssign, new()
            {
                Title = "重新分配项目",
                Route = "projectSystemReAssignList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingThirdAssignment")
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },


        {
            ProjectStatisticsEnum.Level3PendingAssignReview, new()
            {
                Title = "待分配项目",
                Route = "projectSystemPendingAssignReviewList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingThirdAssignment")
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level3PendingReview, new()
            {
                Title = "待复审项目",
                Route = "projectSystemPendingReviewList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingThirdApproval")
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level3PendingApproval, new()
            {
                Title = "已复审通过项目",
                Route = "projectSystemPendingApprovedList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingSecondApproval")
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level3ReviewReturned, new()
            {
                Title = "已退回项目",
                Route = "projectSystemReviewReturnedList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.NotEqual,
                        value: "PendingSecondApproval")
                ],
                CurrentUserIdFieldName = "LastApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level3Approved, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList3",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "Approved")
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = false
            }
        },


        {
            ProjectStatisticsEnum.Level4PendingReview, new()
            {
                Title = "待初审项目",
                Route = "projectSystemPendingReviewList4",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "PendingFourthApproval")
                ],
                CurrentUserIdFieldName = "FourthApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level4PendingApproval, new()
            {
                Title = "已初审通过项目",
                Route = "projectSystemPendingApprovedList4",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.NotEqual,
                        value: "RejectToApply"),
                ],
                CurrentUserIdFieldName = "LastApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level4ReviewReturned, new()
            {
                Title = "已退回项目",
                Route = "projectSystemReviewReturnedList4",
                Status = [FormInstanceStatus.Rejected, FormInstanceStatus.Draft],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: "RejectToApply")
                ],
                CurrentUserIdFieldName = "FourthApprovalUserId",
                Mgt = true,
                UsingNewest = true
            }
        },
        {
            ProjectStatisticsEnum.Level4Approved, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList4",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "TraditionalProject", @operator: FormDataQueryOperator.Equal,
                        value: "True"),
                ],
                CurrentUserIdFieldName = "FourthApprovalUserId",
                Mgt = true,
                UsingNewest = false
            }
        }
    };
}
