using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Itmctr.WebApi.Mgt.Controllers;

[ApiController]
[Route("[controller]")]
public partial class ProjectController : ControllerBase
{
    private readonly IProjectService _projectService;

    public ProjectController(IProjectService projectService)
    {
        _projectService = projectService;
    }

    /// <summary>
    /// 判断
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("judge/{businessId}")]
    public async Task<bool> JudgeAsync(string businessId, [FromBody] JudgeProjectOperationDto input)
    {
        return await _projectService.JudgeAsync(businessId, input);
    }

    /// <summary>
    /// 分配项目
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("assign/{businessId}")]
    public async Task<bool> AssignAsync(string businessId, [FromBody] AssignProjectOperationDto input)
    {
        return await _projectService.AssignAsync(businessId, input);
    }

    /// <summary>
    /// 二审-退回项目
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("return-level2/{businessId}")]
    public async Task<bool> ReturnLevel2Async(string businessId, [FromBody] AssignProjectReturnOperationDto input)
    {
        return await _projectService.ReturnLevel2Async(businessId, input);
    }


    /// <summary>
    /// 分审项目
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("assign-review/{businessId}")]
    public async Task<bool> AssignReviewAsync(string businessId, [FromBody] AssignReviewProjectOperationDto input)
    {
        return await _projectService.AssignReviewAsync(businessId, input);
    }

    /// <summary>
    /// 4审驳回
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="formRejectDto"></param>
    /// <returns></returns>
    [HttpPost("reject/{businessId}/level4")]
    public async Task<bool> RejectLevel4Async(string businessId, [FromBody] FormRejectDto formRejectDto)
    {
        return await _projectService.RejectLevel4Async(businessId, formRejectDto);
    }


    /// <summary>
    /// 4审通过
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("approval/{businessId}/level4")]
    public async Task<bool> ApprovalLevel4Async(string businessId)
    {
        return await _projectService.ApprovalLevel4Async(businessId);
    }


    /// <summary>
    /// 3审驳回
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="formRejectDto"></param>
    /// <returns></returns>
    [HttpPost("reject/{businessId}/level3")]
    public async Task<bool> RejectLevel3Async(string businessId, [FromBody] FormRejectDto formRejectDto)
    {
        return await _projectService.RejectLevel3Async(businessId, formRejectDto);
    }


    /// <summary>
    /// 3审通过
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("approval/{businessId}/level3")]
    public async Task<bool> ApprovalLevel3Async(string businessId)
    {
        return await _projectService.ApprovalLevel3Async(businessId);
    }


    /// <summary>
    /// 2审驳回
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="formRejectDto"></param>
    /// <returns></returns>
    [HttpPost("reject/{businessId}/level2")]
    public async Task<bool> RejectLevel2Async(string businessId, [FromBody] FormRejectDto formRejectDto)
    {
        return await _projectService.RejectLevel2Async(businessId, formRejectDto);
    }


    /// <summary>
    /// 2审通过
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("approval/{businessId}/level2")]
    public async Task<bool> ApprovalLevel2Async(string businessId)
    {
        return await _projectService.ApprovalLevel2Async(businessId);
    }

    /// <summary>
    /// 1审驳回
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("reject/{businessId}/level1")]
    public async Task<bool> RejectLevel1Async(string businessId, [FromBody] SendNumberDto input)
    {
        return await _projectService.RejectLevel1Async(businessId, input);
    }


    /// <summary>
    /// 1审通过
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("approval/{businessId}/level1")]
    public async Task<bool> ApprovalLevel1Async(string businessId, [FromBody] SendNumberDto input)
    {
        return await _projectService.ApprovalLevel1Async(businessId, input);
    }


    /// <summary>
    /// 再修改申请通过
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("edit-confirmed/{businessId}")]
    public async Task<bool> EditConfirmedAsync(string businessId)
    {
        return await _projectService.EditConfirmedAsync(businessId);
    }

    /// <summary>
    /// 再修改申请退回
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("edit-rejected/{businessId}")]
    public async Task<bool> EditRejectedAsync(string businessId)
    {
        return await _projectService.EditRejectedAsync(businessId);
    }


    /// <summary>
    /// 1审 召回
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("recall/{businessId}")]
    public async Task<bool> RecallAsync(string businessId)
    {
        return await _projectService.RecallAsync(businessId);
    }
}
