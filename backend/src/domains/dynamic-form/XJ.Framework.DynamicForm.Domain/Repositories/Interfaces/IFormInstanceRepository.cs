using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.Domain.Repositories.Interfaces;

/// <summary>
/// FormInstance 仓储接口
/// </summary>
public interface IFormInstanceRepository : IAuditRepository<long, FormInstanceEntity>
{
    Task<PageData<long, FormInstanceEntity>> GetPageRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        List<OrderByItem> orderByItems,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        bool isNewest);

    Task<int> GetCountRawSqlAsync(
        List<WhereItem> whereItems,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        bool isNewest = true);

    Task<int> GetCountAsync(Expression<Func<FormInstanceEntity, bool>> whereLambda,
        List<FormDataDynamicQuery> formDataDynamicQueries, bool isNewest);
}
