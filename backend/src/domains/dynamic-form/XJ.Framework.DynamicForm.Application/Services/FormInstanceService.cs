using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text.Json;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace XJ.Framework.DynamicForm.Application.Services;

/// <summary>
/// FormInstance 服务实现
/// </summary>
public sealed partial class FormInstanceService :
    BaseEditableAppService<long, FormInstanceEntity, FormInstanceDto, FormInstanceOperationDto, IFormInstanceRepository,
        FormInstanceQueryCriteria>,
    IFormInstanceService
{
    private readonly IFormRepository _formRepository;
    private readonly IFormService _formService;
    private readonly JsonSerializerOptions _jsonSerializerOptions;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly IFormFieldInstanceService _formFieldInstanceService;
    private readonly IFormInstanceDataService _formInstanceDataService;


    public FormInstanceService(IFormInstanceRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext,
        IFormRepository formRepository, IFormService formService, IOptions<JsonOptions> jsonOptions,
        IFormFieldInstanceService formFieldInstanceService, IFormInstanceDataService formInstanceDataService) : base(
        repository,
        mapper, unitOfWork,
        keyGenerator, currentUserContext)
    {
        _currentUserContext = currentUserContext;
        _formRepository = formRepository;
        _formService = formService;
        _formFieldInstanceService = formFieldInstanceService;
        _formInstanceDataService = formInstanceDataService;
        this._jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }


    public async Task<FormInstanceDto> GetNewestFormInstanceDtoAsync(string businessId)
    {
        var formInstance = (await Repository.GetListAsync(q =>
                !q.IsObsoleted &&
                q.BusinessId.ToLower().Equals(businessId.ToLower()))).OrderByDescending(q => q.VersionTime)
            .FirstOrDefault();

        formInstance.NullCheck();

        return Mapper.Map<FormInstanceDto>(formInstance);
    }

    public async Task<FormInstanceDto> GetNamedVersionFormInstanceDtoAsync(string businessId, string version)
    {
        var formInstance = await Repository.GetAsync(q =>
            q.BusinessId.ToLower().Equals(businessId.ToLower()) &&
            q.Version.ToLower().Equals(version.ToLower()));

        formInstance.NullCheck();

        return Mapper.Map<FormInstanceDto>(formInstance);
    }

    public async Task<FormInstanceDto?> GetNamedInstanceAsync(string businessId, string version)
    {
        var entity = await Repository.GetAsync(q =>
            q.BusinessId.ToLower().Equals(businessId.ToLower()) &&
            q.Version.ToLower().Equals(version.ToLower()));

        return Mapper.Map<FormInstanceDto>(entity);
    }

    public async Task<List<FormInstanceDto>> GetFormInstanceVersionsAsync(string businessId)
    {
        var formInstances = (await Repository.GetListAsync(q =>
            q.BusinessId.ToLower().Equals(businessId.ToLower()))).OrderByDescending(q => q.VersionTime);

        return Mapper.Map<List<FormInstanceDto>>(formInstances);
    }


    public async Task<List<string>> GetValidateAsync(string businessId, string version)
    {
        var formInstanceDto = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await GetValidateAsync(formInstanceDto);
    }

    public async Task<List<string>> GetValidateAsync(string businessId)
    {
        var formInstanceDto = await GetNewestFormInstanceDtoAsync(businessId);
        return await GetValidateAsync(formInstanceDto);
    }

    private async Task<int> GetCountRawSqlAsync(
        bool isNewest,
        List<WhereItem> additionalWhereItems,
        List<FormInstanceStatus>? status,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        var whereItems = new List<WhereItem>();

        whereItems.AddRange(additionalWhereItems);

        if (status is { Count: > 0 })
        {
            whereItems.Add(WhereItemFactory.In("status", status));
        }

        return await Repository.GetCountRawSqlAsync(whereItems, dynamicQueries, formDataDynamicQueries,
            isNewest);
    }

    public async Task<int> GetUserNewestCountAsync(string formCode, List<FormInstanceStatus>? status,
        Dictionary<string, string> dynamicQueries, List<FormDataDynamicQuery> query)
    {
        return await GetCountRawSqlAsync(true,
            [
                WhereItemFactory.Equal("form_code", formCode),
                WhereItemFactory.Equal("apply_user_id", _currentUserContext.GetCurrentUserId())
            ],
            status,
            dynamicQueries,
            query
        );
    }

    public async Task<int> GetUserCountAsync(string formCode, List<FormInstanceStatus>? status,
        Dictionary<string, string> dynamicQueries, List<FormDataDynamicQuery> query)
    {
        return await GetCountRawSqlAsync(false,
            [
                WhereItemFactory.Equal("form_code", formCode),
                WhereItemFactory.Equal("apply_user_id", _currentUserContext.GetCurrentUserId())
            ],
            status,
            dynamicQueries,
            query
        );
    }

    public async Task<int> GetNewestCountAsync(string formCode, List<FormInstanceStatus>? status,
        Dictionary<string, string> dynamicQueries, List<FormDataDynamicQuery> query)
    {
        return await GetCountRawSqlAsync(true,
            [
                WhereItemFactory.Equal("form_code", formCode)
            ],
            status,
            dynamicQueries,
            query
        );
    }

    public async Task<int> GetCountAsync(string formCode, List<FormInstanceStatus>? status,
        Dictionary<string, string> dynamicQueries, List<FormDataDynamicQuery> query)
    {
        return await GetCountRawSqlAsync(false,
            [
                WhereItemFactory.Equal("form_code", formCode)
            ],
            status,
            dynamicQueries,
            query
        );
    }


    public async Task<FormInstancePageDto> GetNewestUserPageAsync(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await GetPageRawSqlAsync(true, criteria,
            WhereItemFactory.Equal("apply_user_id", _currentUserContext.GetCurrentUserId())
        );
    }

    public async Task<FormInstancePageDto> GetUserPageAsync(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await GetPageRawSqlAsync(false, criteria,
            WhereItemFactory.Equal("apply_user_id", _currentUserContext.GetCurrentUserId())
        );
    }


    private async Task<FormInstancePageDto> GetPageRawSqlAsync(
        bool isNewest,
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria,
        params WhereItem[]? additionalWhereItems
    )
    {
        var formDefinition = await _formService.LoadDefinitionAsync(criteria.Condition.FormCode);

        var properties = typeof(FormInstanceEntity).GetProperties();

        var allFieldCodes = formDefinition.Groups.SelectMany(q => q.Fields).Select(q => q.Code);

        var whereItems = criteria.GetWhereItems<long, FormInstanceEntity, FormInstanceQueryCriteria>();

        if (additionalWhereItems != null)
        {
            whereItems.AddRange(additionalWhereItems);
        }

        var orderBys = criteria.OrderBy
            .Select(q =>
            {
                IOrderByRequestItem orderBy =
                    new OrderByRequestItem(q.DataField.CapitalizeFirstLetter(), q.SortDirection);
                return orderBy;
            }).ToList();


        var entityOrderByItems =
            orderBys.Where(p => properties.Any(q => q.Name.ToLower().Equals(p.DataField.ToLower())))
                .ToOrderByItems<long, FormInstanceEntity>()
                .ToList();

        var fieldOrderByItems =
            orderBys.Where(p => allFieldCodes.Contains(p.DataField))
                .ToDictionary(k => k.DataField, v => v.SortDirection == FieldSortDirection.Ascending
                    ? SortDirection.Ascending
                    : SortDirection.Descending);

        var formDataOrderByItems =
            orderBys.Where(p =>
                    !allFieldCodes.Contains(p.DataField) &&
                    !properties.Any(q => q.Name.ToLower().Equals(p.DataField.ToLower())))
                .ToDictionary(k => k.DataField, v => v.SortDirection == FieldSortDirection.Ascending
                    ? SortDirection.Ascending
                    : SortDirection.Descending);


        var data = await ConvertPageDataAsync(await Repository.GetPageRawSqlAsync(
            whereItems,
            criteria.Condition.DynamicQueries,
            criteria.Condition.FormDataDynamicQueries,
            entityOrderByItems,
            fieldOrderByItems,
            formDataOrderByItems,
            criteria.PageParams.ToRowIndex(),
            criteria.PageParams.PageSize,
            isNewest
        ));

        return await ConvertPageDataAsync(formDefinition, data);
    }


    private async Task<FormInstancePageDto> ConvertPageDataAsync(
        FormDefinitionDto formDefinition,
        PageDtoData<long, FormInstanceDto> data
    )
    {
        var formFields =
            await _formFieldInstanceService.GetListByInstanceAsync(data.Rows);

        var formDatas = await _formInstanceDataService.GetListByFormInstanceAsync(data.Rows);

        var result = new FormInstancePageDto
        {
            Totals = data.Totals,
            FormDefinition = formDefinition,
            Rows = data.Rows.SelectAsync(async q =>
            {
                var predicate = _formFieldInstanceService.GetDtoEqualToFormInstanceExpr(q);
                var fields = formFields.Where(predicate.Compile()).ToList();

                var formDataPredicate = _formInstanceDataService.GetDtoEqualToFormInstanceExpr(q);
                var datas = formDatas.Where(formDataPredicate.Compile()).ToList();

                var json = await GetFieldValuesAsync(fields);

                return new FormInstanceJsonDto()
                {
                    Key = q.Key,
                    Status = q.Status,
                    FormCode = q.FormCode,
                    FormVersion = q.FormVersion,
                    Version = q.Version,
                    BusinessId = q.BusinessId,
                    Value = json ?? new Dictionary<string, MultiTypeValue?>(),
                    FormData = datas.ToDictionary(k => k.Code, v => v.Value)
                };
            }).ToEnumerable()
        };

        return result;
    }

    public async new Task<FormInstancePageDto> GetPageAsync(PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await GetPageRawSqlAsync(false, criteria);
    }

    public async new Task<FormInstancePageDto> GetNewestPageAsync(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await GetPageRawSqlAsync(true, criteria);
    }
}
